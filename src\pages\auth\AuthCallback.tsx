import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useNotification } from "@/context/NotificationContext";
import { useAuth } from "@/context/AuthContext";
import { Loader2, CheckCircle, XCircle } from "lucide-react";
import { createOrUpdateOAuthProfile, generateWelcomeMessage } from "@/utils/oauthProfileHandler";

const AuthCallback = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  const { refreshProfile } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processando autenticação...');
  const [hasProcessed, setHasProcessed] = useState(false);

  useEffect(() => {
    // Evitar processamento múltiplo
    if (hasProcessed) return;

    const handleAuthCallback = async () => {
      setHasProcessed(true);
      try {
        // Obter a sessão do URL hash/query params
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          throw error;
        }

        if (data.session) {
          setStatus('success');
          setMessage('Login realizado com sucesso!');

          const user = data.session.user;

          // Criar ou atualizar perfil OAuth
          const profileResult = await createOrUpdateOAuthProfile(user);

          if (!profileResult.success) {
            console.error('Erro ao processar perfil OAuth:', profileResult.error);
            // Não bloquear o login por erro no perfil, apenas logar
          }

          // Forçar refresh do perfil no AuthContext para carregar dados atualizados
          await refreshProfile(user.id, user.email);

          // Gerar mensagem de boas-vindas personalizada
          const welcomeMessage = generateWelcomeMessage(user, profileResult.isNewUser);

          showNotification({
            title: welcomeMessage.title,
            description: welcomeMessage.description,
            type: "success",
            buttonText: "Continuar"
          });

          // Redirecionar após um breve delay
          setTimeout(() => {
            navigate('/', { replace: true });
          }, 2000);

        } else {
          throw new Error('Nenhuma sessão encontrada');
        }

      } catch (error: any) {
        setStatus('error');
        
        let errorMessage = "Erro ao processar autenticação.";
        
        if (error.message?.includes('access_denied')) {
          errorMessage = "Acesso negado. Você cancelou o login ou negou as permissões.";
        } else if (error.message?.includes('invalid_request')) {
          errorMessage = "Solicitação inválida. Tente fazer login novamente.";
        } else if (error.message?.includes('network')) {
          errorMessage = "Erro de conexão. Verifique sua internet.";
        }

        setMessage(errorMessage);

        showNotification({
          title: "Erro na autenticação",
          description: errorMessage,
          type: "error",
          buttonText: "Tentar novamente"
        });

        // Redirecionar para login após erro
        setTimeout(() => {
          navigate('/', { replace: true });
        }, 3000);
      }
    };

    handleAuthCallback();
  }, [navigate, showNotification, hasProcessed, refreshProfile]);

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-8 w-8 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'error':
        return <XCircle className="h-8 w-8 text-red-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="flex justify-center mb-4">
            {getIcon()}
          </div>
          
          <h1 className={`text-xl font-semibold mb-2 ${getStatusColor()}`}>
            {status === 'loading' && 'Processando...'}
            {status === 'success' && 'Sucesso!'}
            {status === 'error' && 'Erro'}
          </h1>
          
          <p className="text-gray-600 mb-4">
            {message}
          </p>

          {status === 'loading' && (
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
          )}

          {status === 'error' && (
            <button
              onClick={() => navigate('/', { replace: true })}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Voltar ao início
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthCallback;
