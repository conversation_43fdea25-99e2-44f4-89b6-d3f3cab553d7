import { useEffect, useRef } from 'react';

interface RenderLogData {
  [key: string]: any;
}

// Contador global de renders para tracking
let globalRenderCount = 0;
const componentRenderCounts = new Map<string, number>();

/**
 * Hook para detectar TODOS os renders de componentes
 * Logs CADA render para debugging detalhado
 */
export const useRenderLogger = (componentName: string, data?: RenderLogData) => {
  const renderCount = useRef(0);
  const lastLogTime = useRef(Date.now());
  const renderTimes = useRef<number[]>([]);

  // SEMPRE fazer log de CADA render
  renderCount.current += 1;
  globalRenderCount += 1;
  componentRenderCounts.set(componentName, (componentRenderCounts.get(componentName) || 0) + 1);

  const now = Date.now();
  renderTimes.current.push(now);

  // Manter apenas os últimos 20 renders para análise
  if (renderTimes.current.length > 20) {
    renderTimes.current = renderTimes.current.slice(-20);
  }

  // Calcular renders por segundo
  const fiveSecondsAgo = now - 5000;
  const recentRenders = renderTimes.current.filter(time => time > fiveSecondsAgo);
  const rendersPerSecond = recentRenders.length / 5;

  // Warning para renders excessivos
  if (rendersPerSecond > 10) {
    console.warn(`🚨 [EXCESSIVE] ${componentName} - ${rendersPerSecond.toFixed(2)} renders/sec!`);
  }

  lastLogTime.current = now;

  useEffect(() => {
    // Effect tracking without logging
  });

  return {
    renderCount: renderCount.current,
    rendersPerSecond,
    globalRenderCount
  };
};

/**
 * Função utilitária para log manual de operações
 */
export const logOperation = (operation: string, data?: any) => {
  // Operation tracking without logging
};

/**
 * Função utilitária para log de erros
 */
export const logError = (error: string, data?: any) => {
  console.error(`❌ [ERROR] ${error}:`, {
    ...data,
    timestamp: new Date().toISOString()
  });
};

/**
 * Hook para detectar EXATAMENTE quais dependências estão mudando
 */
export const useDependencyTracker = (name: string, dependencies: any[]) => {
  const prevDeps = useRef<any[]>([]);

  useEffect(() => {
    const changes: string[] = [];

    dependencies.forEach((dep, index) => {
      const prevDep = prevDeps.current[index];
      if (prevDep !== dep) {
        // Detectar tipo de mudança
        let changeType = 'value';
        if (typeof dep === 'object' && dep !== null) {
          changeType = 'object_reference';
        } else if (typeof dep === 'function') {
          changeType = 'function_reference';
        }

        changes.push(`[${index}] ${changeType}: ${JSON.stringify(prevDep)} → ${JSON.stringify(dep)}`);
      }
    });

    if (changes.length > 0) {
      console.warn(`🔍 [DEPENDENCY_CHANGE] ${name}:`, {
        changes,
        timestamp: new Date().toISOString().split('T')[1],
        stackTrace: new Error().stack?.split('\n').slice(2, 4).join(' → ')
      });
    }

    prevDeps.current = [...dependencies];
  });
};
