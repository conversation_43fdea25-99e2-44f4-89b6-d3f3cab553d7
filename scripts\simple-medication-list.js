import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Configuração do Supabase
const supabaseUrl = 'https://bxedpdmgvgatjdfxgxij.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo';

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  console.log('🔍 Buscando medicamentos...');
  
  try {
    const { data, error } = await supabase
      .from('pedbook_medications')
      .select(`
        id,
        name,
        slug,
        brands,
        pedbook_medication_categories (
          name
        )
      `)
      .order('name', { ascending: true });

    if (error) {
      console.error('❌ Erro:', error);
      return;
    }

    console.log(`✅ Encontrados ${data.length} medicamentos`);
    
    // Criar diretório
    if (!fs.existsSync('bulas')) {
      fs.mkdirSync('bulas', { recursive: true });
    }
    
    // Processar dados
    const medications = data.map((med, index) => ({
      index: index + 1,
      name: med.name,
      slug: med.slug,
      brands: med.brands || '',
      category: med.pedbook_medication_categories?.name || 'Sem categoria'
    }));
    
    // Salvar lista
    fs.writeFileSync('bulas/medications-list.json', JSON.stringify(medications, null, 2));
    
    console.log('📝 Lista salva em bulas/medications-list.json');
    console.log('\n📋 Primeiros 10 medicamentos:');
    medications.slice(0, 10).forEach(med => {
      console.log(`${med.index}. ${med.name} (${med.category})`);
    });
    
  } catch (error) {
    console.error('❌ Erro:', error);
  }
}

main();
