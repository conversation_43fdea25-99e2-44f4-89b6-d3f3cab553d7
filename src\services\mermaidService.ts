// Serviço para renderização de Mermaid
// Simula uma API de renderização para desenvolvimento

export interface MermaidRenderRequest {
  diagram_definition: string;
  title?: string;
}

export interface MermaidRenderResponse {
  success: boolean;
  svg?: string;
  error?: string;
}

export const renderMermaidDiagram = async (request: MermaidRenderRequest): Promise<MermaidRenderResponse> => {
  try {
    // Simular delay de rede
    await new Promise(resolve => setTimeout(resolve, 500));

    const { diagram_definition, title = 'Mapa Mental' } = request;

    if (!diagram_definition) {
      return {
        success: false,
        error: 'diagram_definition is required'
      };
    }

    // Gerar SVG mock baseado no título
    const mockSvg = `
      <div class="mermaid-rendered">
        <svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
          <!-- Fundo -->
          <rect width="500" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
          
          <!-- Nó central -->
          <circle cx="250" cy="200" r="90" fill="#e3f2fd" stroke="#1976d2" stroke-width="3"/>
          <text x="250" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1976d2">
            ${title}
          </text>
          
          <!-- Nós secundários -->
          <circle cx="120" cy="100" r="50" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2"/>
          <text x="120" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#7b1fa2">
            Conceito 1
          </text>
          
          <circle cx="380" cy="100" r="50" fill="#e8f5e8" stroke="#388e3c" stroke-width="2"/>
          <text x="380" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#388e3c">
            Conceito 2
          </text>
          
          <circle cx="120" cy="300" r="50" fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
          <text x="120" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#f57c00">
            Conceito 3
          </text>
          
          <circle cx="380" cy="300" r="50" fill="#ffebee" stroke="#d32f2f" stroke-width="2"/>
          <text x="380" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#d32f2f">
            Conceito 4
          </text>
          
          <!-- Linhas conectoras -->
          <line x1="250" y1="200" x2="120" y2="100" stroke="#666" stroke-width="2"/>
          <line x1="250" y1="200" x2="380" y2="100" stroke="#666" stroke-width="2"/>
          <line x1="250" y1="200" x2="120" y2="300" stroke="#666" stroke-width="2"/>
          <line x1="250" y1="200" x2="380" y2="300" stroke="#666" stroke-width="2"/>
          
          <!-- Detalhes adicionais -->
          <circle cx="70" cy="50" r="25" fill="#e1f5fe" stroke="#0277bd" stroke-width="1"/>
          <text x="70" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0277bd">
            Sub 1
          </text>
          
          <circle cx="170" cy="50" r="25" fill="#e1f5fe" stroke="#0277bd" stroke-width="1"/>
          <text x="170" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0277bd">
            Sub 2
          </text>
          
          <circle cx="430" cy="50" r="25" fill="#f1f8e9" stroke="#558b2f" stroke-width="1"/>
          <text x="430" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#558b2f">
            Sub 3
          </text>
          
          <circle cx="330" cy="50" r="25" fill="#f1f8e9" stroke="#558b2f" stroke-width="1"/>
          <text x="330" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#558b2f">
            Sub 4
          </text>
          
          <!-- Linhas para sub-conceitos -->
          <line x1="120" y1="100" x2="70" y2="50" stroke="#999" stroke-width="1"/>
          <line x1="120" y1="100" x2="170" y2="50" stroke="#999" stroke-width="1"/>
          <line x1="380" y1="100" x2="330" y2="50" stroke="#999" stroke-width="1"/>
          <line x1="380" y1="100" x2="430" y2="50" stroke="#999" stroke-width="1"/>
        </svg>
        
        <div class="mt-3 text-center text-sm text-gray-600">
          <p>Mapa mental renderizado com sucesso</p>
          <p class="text-xs text-gray-500 mt-1">Versão simplificada para desenvolvimento</p>
        </div>
      </div>
    `;

    return {
      success: true,
      svg: mockSvg
    };

  } catch (error) {
    console.error('Erro ao renderizar Mermaid:', error);
    return {
      success: false,
      error: 'Erro interno na renderização'
    };
  }
};

// Função para simular a API endpoint
export const mockMermaidAPI = async (request: MermaidRenderRequest): Promise<Response> => {
  const result = await renderMermaidDiagram(request);
  
  if (result.success) {
    return new Response(result.svg, {
      status: 200,
      headers: {
        'Content-Type': 'text/html'
      }
    });
  } else {
    return new Response(JSON.stringify({ error: result.error }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
