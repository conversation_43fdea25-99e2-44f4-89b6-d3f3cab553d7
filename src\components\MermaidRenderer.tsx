import React, { useEffect, useRef, useState } from 'react';
import { Copy, Download, ZoomIn, ZoomOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { renderMermaidDiagram } from '@/services/mermaidService';

interface MermaidRendererProps {
  code: string;
  title?: string;
  className?: string;
}

export const MermaidRenderer: React.FC<MermaidRendererProps> = ({
  code,
  title = "Mapa Mental",
  className = ""
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [zoom, setZoom] = useState(1);

  useEffect(() => {
    renderMermaid();
  }, [code]);

  const renderMermaid = async () => {
    if (!containerRef.current || !code) return;

    setIsLoading(true);
    setError(null);

    try {
      // Limpar container
      containerRef.current.innerHTML = '';

      // Usar o serviço local de renderização
      const result = await renderMermaidDiagram({
        diagram_definition: code,
        title: title
      });

      if (!result.success) {
        throw new Error(result.error || 'Falha ao renderizar Mermaid');
      }

      const svgContent = result.svg || '';
      
      // Inserir SVG no container
      containerRef.current.innerHTML = svgContent;
      
      // Aplicar zoom se necessário
      const svgElement = containerRef.current.querySelector('svg');
      if (svgElement) {
        svgElement.style.transform = `scale(${zoom})`;
        svgElement.style.transformOrigin = 'top left';
      }

    } catch (err) {
      console.error('Erro ao renderizar Mermaid:', err);
      setError('Não foi possível renderizar o mapa mental');
      
      // Fallback: mostrar código como texto
      if (containerRef.current) {
        containerRef.current.innerHTML = `
          <div class="p-4 bg-gray-50 border border-gray-200 rounded">
            <p class="text-gray-600 mb-2">Código Mermaid:</p>
            <pre class="text-sm text-gray-800 whitespace-pre-wrap">${code}</pre>
          </div>
        `;
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      toast({
        title: "Código copiado!",
        description: "O código Mermaid foi copiado para a área de transferência."
      });
    } catch (err) {
      toast({
        title: "Erro ao copiar",
        description: "Não foi possível copiar o código.",
        variant: "destructive"
      });
    }
  };

  const handleDownload = () => {
    const svgElement = containerRef.current?.querySelector('svg');
    if (!svgElement) return;

    try {
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);
      
      const downloadLink = document.createElement('a');
      downloadLink.href = svgUrl;
      downloadLink.download = `mapa-mental-${Date.now()}.svg`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(svgUrl);

      toast({
        title: "Download iniciado!",
        description: "O mapa mental foi baixado como SVG."
      });
    } catch (err) {
      toast({
        title: "Erro no download",
        description: "Não foi possível baixar o mapa mental.",
        variant: "destructive"
      });
    }
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(zoom + 0.2, 3);
    setZoom(newZoom);
    
    const svgElement = containerRef.current?.querySelector('svg');
    if (svgElement) {
      svgElement.style.transform = `scale(${newZoom})`;
    }
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom - 0.2, 0.5);
    setZoom(newZoom);
    
    const svgElement = containerRef.current?.querySelector('svg');
    if (svgElement) {
      svgElement.style.transform = `scale(${newZoom})`;
    }
  };

  return (
    <div className={`mb-4 bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-3">
        <div className="flex items-center justify-between">
          <span className="text-white font-semibold">🧠 {title}</span>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="secondary"
              onClick={handleZoomOut}
              disabled={zoom <= 0.5}
              className="h-7 w-7 p-0"
            >
              <ZoomOut className="h-3 w-3" />
            </Button>
            <span className="text-white text-sm min-w-[3rem] text-center">
              {Math.round(zoom * 100)}%
            </span>
            <Button
              size="sm"
              variant="secondary"
              onClick={handleZoomIn}
              disabled={zoom >= 3}
              className="h-7 w-7 p-0"
            >
              <ZoomIn className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={handleCopyCode}
              className="h-7 px-2"
            >
              <Copy className="h-3 w-3 mr-1" />
              Copiar
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={handleDownload}
              className="h-7 px-2"
            >
              <Download className="h-3 w-3 mr-1" />
              Baixar
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {isLoading && (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            <span className="ml-2 text-gray-600">Renderizando mapa mental...</span>
          </div>
        )}

        {error && (
          <div className="text-center text-red-500 p-4">
            <p>{error}</p>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={renderMermaid}
              className="mt-2"
            >
              Tentar Novamente
            </Button>
          </div>
        )}

        <div 
          ref={containerRef}
          className="mermaid-container overflow-auto"
          style={{ 
            minHeight: isLoading ? '200px' : 'auto',
            maxHeight: '600px'
          }}
        />
      </div>
    </div>
  );
};

export default MermaidRenderer;
