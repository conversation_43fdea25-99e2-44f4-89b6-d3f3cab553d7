import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuração do Supabase
const supabaseUrl = 'https://bxedpdmgvgatjdfxgxij.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo';

const supabase = createClient(supabaseUrl, supabaseKey);

// Função para criar diretório se não existir
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Função para buscar todos os medicamentos com categorias
async function fetchAllMedicationsWithCategories() {
  console.log('🔍 Buscando todos os medicamentos com categorias...');
  
  try {
    const { data, error } = await supabase
      .from('pedbook_medications')
      .select(`
        id,
        name,
        slug,
        brands,
        description,
        pedbook_medication_categories (
          name
        )
      `)
      .order('name', { ascending: true });

    if (error) {
      console.error('❌ Erro ao buscar medicamentos:', error);
      return [];
    }

    console.log(`✅ Encontrados ${data.length} medicamentos`);
    return data;
  } catch (error) {
    console.error('❌ Erro na consulta:', error);
    return [];
  }
}

// Função para salvar lista completa de medicamentos
async function saveMedicationsList() {
  const medications = await fetchAllMedicationsWithCategories();
  
  if (medications.length === 0) {
    console.log('❌ Nenhum medicamento encontrado');
    return [];
  }

  // Criar diretório para as bulas
  const bulasDir = path.join(__dirname, '..', 'bulas');
  ensureDirectoryExists(bulasDir);

  // Processar e organizar dados
  const medicationsList = medications.map((med, index) => {
    const brands = med.brands ? med.brands.split(',').map(b => b.trim()) : [];
    return {
      index: index + 1,
      id: med.id,
      name: med.name,
      slug: med.slug,
      brands: brands,
      brandCount: brands.length,
      category: med.pedbook_medication_categories?.name || 'Sem categoria',
      description: med.description || '',
      searchTerms: [med.name, ...brands]
    };
  });

  // Salvar lista completa
  const listPath = path.join(bulasDir, 'medications-complete-list.json');
  fs.writeFileSync(listPath, JSON.stringify(medicationsList, null, 2), 'utf8');
  
  // Salvar lista resumida para processamento
  const summaryList = medicationsList.map(med => ({
    index: med.index,
    name: med.name,
    slug: med.slug,
    category: med.category,
    brandCount: med.brandCount,
    priority: calculatePriority(med)
  }));

  const summaryPath = path.join(bulasDir, 'medications-summary.json');
  fs.writeFileSync(summaryPath, JSON.stringify(summaryList, null, 2), 'utf8');
  
  console.log(`📝 Lista completa salva em: ${listPath}`);
  console.log(`📋 Lista resumida salva em: ${summaryPath}`);
  console.log(`📊 Total de medicamentos: ${medicationsList.length}`);
  
  return medicationsList;
}

// Função para calcular prioridade de coleta
function calculatePriority(medication) {
  let priority = 0;
  
  // Medicamentos com mais marcas = maior prioridade
  priority += medication.brandCount * 2;
  
  // Categorias prioritárias
  const highPriorityCategories = [
    'Analg', 'Antimicrobianos', 'Anti-histam', 'Corticoides'
  ];
  
  if (highPriorityCategories.some(cat => medication.category.includes(cat))) {
    priority += 10;
  }
  
  // Medicamentos pediátricos comuns
  const commonPediatric = [
    'paracetamol', 'ibuprofeno', 'dipirona', 'amoxicilina', 
    'prednisolona', 'loratadina', 'salbutamol'
  ];
  
  if (commonPediatric.some(med => medication.name.toLowerCase().includes(med))) {
    priority += 15;
  }
  
  return priority;
}

// Função para buscar bula online (placeholder para implementação futura)
async function searchLeafletOnline(medication) {
  console.log(`🔍 Buscando bula para: ${medication.name}`);
  
  const searchSources = [
    {
      name: 'ANVISA Bulário',
      url: 'https://consultas.anvisa.gov.br/#/medicamentos/',
      priority: 1
    },
    {
      name: 'ANVISA Consultas',
      url: 'https://www.gov.br/anvisa/pt-br/assuntos/medicamentos/bulario',
      priority: 2
    }
  ];

  // Simular busca (implementação real requer web scraping ou browser automation)
  const results = [];
  
  for (const term of medication.searchTerms.slice(0, 3)) { // Limitar a 3 termos
    results.push({
      searchTerm: term,
      found: false,
      source: null,
      content: null,
      error: 'Busca automatizada não implementada - requer browser automation'
    });
  }
  
  return {
    medication: medication.name,
    totalSearches: results.length,
    found: results.some(r => r.found),
    results: results,
    timestamp: new Date().toISOString()
  };
}

// Função para processar um medicamento específico
async function processMedication(medication) {
  console.log(`\n🔄 Processando [${medication.index}/138]: ${medication.name}`);
  console.log(`   📂 Categoria: ${medication.category}`);
  console.log(`   🏷️  Marcas: ${medication.brandCount}`);
  
  // Criar diretório para o medicamento
  const medicationDir = path.join(__dirname, '..', 'bulas', medication.slug);
  ensureDirectoryExists(medicationDir);
  
  // Buscar bula online
  const searchResult = await searchLeafletOnline(medication);
  
  // Salvar metadados
  const metadata = {
    medication: medication,
    searchResult: searchResult,
    processedAt: new Date().toISOString(),
    status: searchResult.found ? 'found' : 'not_found',
    nextSteps: searchResult.found ? 
      ['validate_content', 'save_pdf'] : 
      ['manual_search', 'browser_automation']
  };
  
  const metadataPath = path.join(medicationDir, 'metadata.json');
  fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2), 'utf8');
  
  // Criar arquivo README para o medicamento
  const readmePath = path.join(medicationDir, 'README.md');
  const readmeContent = `# ${medication.name}

## Informações Básicas
- **Nome:** ${medication.name}
- **Categoria:** ${medication.category}
- **Marcas Comerciais:** ${medication.brandCount}
- **Status da Bula:** ${searchResult.found ? '✅ Encontrada' : '❌ Não encontrada'}

## Marcas Comerciais
${medication.brands.map(brand => `- ${brand}`).join('\n')}

## Próximos Passos
${metadata.nextSteps.map(step => `- [ ] ${step}`).join('\n')}

## Histórico
- **Processado em:** ${new Date().toLocaleString('pt-BR')}
- **Buscas realizadas:** ${searchResult.totalSearches}
`;

  fs.writeFileSync(readmePath, readmeContent, 'utf8');
  
  if (searchResult.found) {
    console.log(`   ✅ Bula encontrada para ${medication.name}`);
  } else {
    console.log(`   ❌ Bula não encontrada para ${medication.name}`);
  }
  
  return metadata;
}

// Função principal
async function main() {
  console.log('🚀 Iniciando coleta avançada de bulas de medicamentos...\n');
  
  // Salvar lista de medicamentos
  const medications = await saveMedicationsList();
  
  if (!medications || medications.length === 0) {
    console.log('❌ Não foi possível obter a lista de medicamentos');
    return;
  }
  
  // Ordenar por prioridade
  const sortedMedications = medications.sort((a, b) => 
    calculatePriority(b) - calculatePriority(a)
  );
  
  console.log('\n🎯 Top 10 medicamentos por prioridade:');
  sortedMedications.slice(0, 10).forEach((med, index) => {
    console.log(`${index + 1}. ${med.name} (${med.category}) - Prioridade: ${calculatePriority(med)}`);
  });
  
  console.log('\n📋 Iniciando processamento dos medicamentos...');
  console.log('⚠️  NOTA: Busca automatizada requer implementação de browser automation\n');
  
  // Processar apenas os primeiros 5 medicamentos como demonstração
  const results = [];
  for (let i = 0; i < Math.min(5, sortedMedications.length); i++) {
    const result = await processMedication(sortedMedications[i]);
    results.push(result);
    
    // Pequena pausa entre processamentos
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Salvar relatório de processamento
  const reportPath = path.join(__dirname, '..', 'bulas', 'processing-report.json');
  const report = {
    timestamp: new Date().toISOString(),
    totalMedications: medications.length,
    processed: results.length,
    found: results.filter(r => r.searchResult.found).length,
    notFound: results.filter(r => !r.searchResult.found).length,
    results: results
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
  
  console.log('\n✅ Processamento concluído!');
  console.log(`📁 Verifique a pasta "bulas" para os resultados`);
  console.log(`📊 Relatório salvo em: ${reportPath}`);
  console.log(`\n📈 Estatísticas:`);
  console.log(`   - Total de medicamentos: ${medications.length}`);
  console.log(`   - Processados: ${results.length}`);
  console.log(`   - Encontrados: ${report.found}`);
  console.log(`   - Não encontrados: ${report.notFound}`);
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { 
  fetchAllMedicationsWithCategories, 
  processMedication, 
  saveMedicationsList,
  calculatePriority 
};
