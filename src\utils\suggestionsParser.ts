// 🎯 Parser simples para sugestões do Dr. Will
// Formato: $~~~SUGGESTIONS$ ... $~~~SUGGESTIONS$

export interface Suggestion {
  text: string;
  action: string;
}

export interface ParsedSuggestions {
  content: string; // Conteúdo sem as sugestões
  suggestions: Suggestion[];
  hasSuggestions: boolean;
}

/**
 * Extrai sugestões do conteúdo do Dr. Will
 * @param content - Conteúdo da mensagem
 * @returns Objeto com conteúdo limpo e sugestões extraídas
 */
export function parseSuggestions(content: string): ParsedSuggestions {
  if (!content || typeof content !== 'string') {
    return {
      content: content || '',
      suggestions: [],
      hasSuggestions: false
    };
  }

  // Regex para encontrar blocos de sugestões
  const suggestionsRegex = /\$~~~SUGGESTIONS\$([\s\S]*?)\$~~~SUGGESTIONS\$/g;
  
  let suggestions: Suggestion[] = [];
  let cleanContent = content;
  let match;

  // Encontrar todas as ocorrências de sugestões
  while ((match = suggestionsRegex.exec(content)) !== null) {
    const suggestionsBlock = match[1].trim();
    
    try {
      // Tentar parsear como JSON array
      if (suggestionsBlock.startsWith('[') && suggestionsBlock.endsWith(']')) {
        const parsedSuggestions = JSON.parse(suggestionsBlock);
        if (Array.isArray(parsedSuggestions)) {
          suggestions.push(...parsedSuggestions.filter(s => s.text && s.action));
        }
      } else {
        // Parsear linha por linha (formato alternativo)
        const lines = suggestionsBlock.split('\n').filter(line => line.trim());
        for (const line of lines) {
          try {
            const parsed = JSON.parse(line.trim().replace(/,$/, ''));
            if (parsed.text && parsed.action) {
              suggestions.push(parsed);
            }
          } catch {
            // Ignorar linhas que não são JSON válido
          }
        }
      }
    } catch (error) {
      // Se não conseguir parsear, ignorar este bloco
      console.warn('Erro ao parsear sugestões:', error);
    }

    // Remover o bloco de sugestões do conteúdo
    cleanContent = cleanContent.replace(match[0], '').trim();
  }

  return {
    content: cleanContent,
    suggestions: suggestions.slice(0, 4), // Máximo 4 sugestões
    hasSuggestions: suggestions.length > 0
  };
}

/**
 * Valida se uma sugestão é válida
 * @param suggestion - Objeto sugestão
 * @returns true se válida
 */
export function isValidSuggestion(suggestion: any): suggestion is Suggestion {
  return (
    suggestion &&
    typeof suggestion === 'object' &&
    typeof suggestion.text === 'string' &&
    typeof suggestion.action === 'string' &&
    suggestion.text.trim().length > 0 &&
    suggestion.action.trim().length > 0 &&
    suggestion.text.length <= 30 // Máximo 30 caracteres para o texto
  );
}

/**
 * Limpa e valida sugestões
 * @param suggestions - Array de sugestões
 * @returns Array de sugestões válidas
 */
export function cleanSuggestions(suggestions: any[]): Suggestion[] {
  if (!Array.isArray(suggestions)) return [];
  
  return suggestions
    .filter(isValidSuggestion)
    .map(s => ({
      text: s.text.trim(),
      action: s.action.trim()
    }))
    .slice(0, 4); // Máximo 4 sugestões
}
