import { useState, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { measureOperation } from '@/utils/drWillMonitor';
import { drWillLogger } from '@/utils/logger';

export interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isStreaming?: boolean;
  isThinking?: boolean;
  needsFinalFormat?: boolean; // 🎯 Flag para formatação final após streaming
  images?: string[]; // 🖼️ URLs das imagens anexadas
}

export interface QuestionContext {
  questionNumber: number;
  totalQuestions: number;
  sessionTitle: string;
  sessionId: string;
  questionId: string;
  specialty?: string;
  theme?: string;
  focus?: string;
  statement?: string;
  alternatives?: Array<{ text: string; letter: string }>;
  correctAnswer?: string;
  explanation?: string;
  // 🎯 NOVOS CAMPOS PARA CONTEXTO ENRIQUECIDO
  examYear?: number;
  examLocation?: string;
  assessmentType?: string;
  knowledgeDomain?: string;
  questionFormat?: string;
  contentTags?: any;
  aiCommentary?: any;
}

interface UseDrWillContextualChatProps {
  currentThreadId: string | null;
  saveToHistory: (message: any, threadId?: string) => Promise<string | null>;
  createNewThread: (firstMessage?: string, sessionId?: string) => Promise<string | null>;
  findOrCreateContextualThread: (sessionTitle: string, sessionId: string) => Promise<string | null>;
  getConversationHistory: (threadId?: string) => Promise<Array<{role: 'user' | 'assistant', content: string}>>;
  questionContext?: QuestionContext | null;
}

export const useDrWillContextualChat = ({
  currentThreadId,
  saveToHistory,
  createNewThread,
  findOrCreateContextualThread,
  getConversationHistory,
  questionContext
}: UseDrWillContextualChatProps) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const addMessage = useCallback((message: ChatMessage) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const updateMessage = useCallback((messageId: string, updates: Partial<ChatMessage>) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId ? { ...msg, ...updates } : msg
    ));
  }, []);

  // Função para enviar mensagem contextual
  const sendContextualMessage = useCallback(async (
    content: string,
    contextOverride?: QuestionContext,
    userId?: string,
    forceThreadId?: string | null,
    imageUrl?: string | string[]
  ) => {
    if (!content.trim() || isLoading) return;

    // Usar questionContext do hook ou override passado como parâmetro
    const currentQuestionContext = contextOverride || questionContext;

    if (!currentQuestionContext) {
      return;
    }

    return await measureOperation('sendContextualMessage', async () => {
      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      // 🎯 SEMPRE buscar thread específica para a sessão (ignorar currentThreadId)
      // Isso garante que cada sessão tenha sua própria thread contextual
      let threadId = forceThreadId;

      // SEMPRE buscar ou criar thread específica para esta sessão
      if (!threadId) {
        threadId = await findOrCreateContextualThread(
          currentQuestionContext.sessionTitle,
          currentQuestionContext.sessionId
        );
        if (!threadId) {
          drWillLogger.error('sendContextualMessage', 'Failed to find or create session thread');
          return;
        }
      }

      // Add user message
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        content: content.trim(),
        isUser: true,
        timestamp: new Date()
      };

      addMessage(userMessage);

      // Save user message to history
      const savedMessageId = await saveToHistory({
        id: userMessage.id,
        content: userMessage.content,
        isUser: true,
        timestamp: userMessage.timestamp
      }, threadId);

      if (!savedMessageId) {
        drWillLogger.error('saving user message to history', 'Failed to save');
        setError('Erro ao salvar mensagem');
        setIsLoading(false);
        return;
      }

      // Get conversation history
      await new Promise(resolve => setTimeout(resolve, 100));
      const conversationHistory = await getConversationHistory(threadId);

      setIsLoading(true);
      setIsStreaming(true);
      setError(null);

      // Process contextual response
      await processContextualResponse(content, currentQuestionContext, threadId, conversationHistory, userId);
    });
  }, [isLoading, addMessage, updateMessage, currentThreadId, createNewThread, saveToHistory, getConversationHistory, questionContext, findOrCreateContextualThread]);

  // Função para processar resposta contextual
  const processContextualResponse = async (
    content: string,
    questionContext: QuestionContext,
    threadId: string,
    conversationHistory: Array<{role: 'user' | 'assistant', content: string}>,
    userId?: string
  ) => {
    // Create AI response message (inicialmente para thinking)
    const aiMessageId = (Date.now() + 1).toString();
    const aiMessage: ChatMessage = {
      id: aiMessageId,
      content: '',
      isUser: false,
      timestamp: new Date(),
      isStreaming: true,
      isThinking: true
    };

    addMessage(aiMessage);

    try {
      // Get Supabase URL and key for direct function call
      const supabaseUrl = supabase.supabaseUrl;
      const supabaseKey = supabase.supabaseKey;



      const requestBody = {
        message: content,
        userId: userId,
        conversationHistory: conversationHistory,
        questionContext: questionContext,
        image_url: imageUrl
      };

      // Create a timeout signal
      const timeoutController = new AbortController();
      const timeoutId = setTimeout(() => timeoutController.abort(), 30000); // 30 seconds

      const response = await fetch(`${supabaseUrl}/functions/v1/dr-will-question-context`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortControllerRef.current?.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${response.status} - ${errorText}`);
      }

      // Process streaming response (IGUAL AO DR. WILL PRINCIPAL)
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Response stream não disponível');
      }

      const decoder = new TextDecoder();
      let fullThinkingContent = '';
      let fullDrWillContent = '';
      let chunkCount = 0;
      let thinkingPhaseComplete = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.trim() === '') continue;
          if (!line.startsWith('data: ')) continue;

          const data = line.slice(6);
          if (data === '[DONE]') {
            continue;
          }

          try {
            const parsed = JSON.parse(data);

            // Handle completion
            if (parsed.done) {
              break;
            }

            if (parsed.content) {
              if (parsed.isThinking === true) {
                // Conteúdo de thinking
                fullThinkingContent += parsed.content;

                // Exibir thinking em tempo real
                updateMessage(aiMessageId, {
                  content: fullThinkingContent,
                  isStreaming: true,
                  isThinking: true
                });
              } else {
                // Conteúdo da resposta final
                if (!thinkingPhaseComplete) {
                  // Primeira vez que recebemos resposta final
                  thinkingPhaseComplete = true;
                }

                // Acumular resposta final
                fullDrWillContent += parsed.content;

                // Atualizar mensagem em tempo real com a resposta final
                updateMessage(aiMessageId, {
                  content: fullDrWillContent,
                  isStreaming: true,
                  isThinking: false
                });
              }
            }

            // Handle errors
            if (parsed.error) {
              throw new Error(parsed.error);
            }
          } catch (parseError) {
            // Silently ignore parse errors
          }
        }
      }

      // Finalize message
      updateMessage(aiMessageId, {
        content: fullDrWillContent,
        isStreaming: false,
        isThinking: false
      });

      // Save AI response to history (APENAS resposta do Dr. Will)
      if (fullDrWillContent && threadId) {
        const savedAiMessageId = await saveToHistory({
          id: aiMessageId,
          content: fullDrWillContent,
          isUser: false,
          timestamp: new Date()
        }, threadId);

        if (!savedAiMessageId) {
          drWillLogger.error('saving AI response to history', 'Failed to save');
        }
      }

    } catch (error: any) {
      let errorMessage = 'Erro inesperado. Tente novamente.';

      if (error.message?.includes('API error')) {
        errorMessage = 'Erro de conexão com Dr. Will. Verifique sua internet e tente novamente.';
      } else if (error.message?.includes('timeout')) {
        errorMessage = 'Dr. Will está demorando para responder. Tente uma pergunta mais simples.';
      } else if (error.message?.includes('rate limit')) {
        errorMessage = 'Muitas perguntas simultâneas. Aguarde um momento antes de tentar novamente.';
      }

      updateMessage(aiMessageId, {
        content: `❌ **Erro**: ${errorMessage}

💡 **Dicas para tentar novamente:**
- Verifique sua conexão com a internet
- Tente uma pergunta mais específica sobre a questão
- Aguarde alguns segundos antes de tentar novamente

Se o problema persistir, entre em contato com o suporte.`,
        isStreaming: false
      });

      setError(errorMessage);
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
    }
  };

  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  const setMessagesDirectly = useCallback((newMessages: ChatMessage[]) => {
    setMessages(newMessages);
  }, []);

  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return {
    messages,
    isLoading,
    isStreaming,
    error,
    sendContextualMessage,
    clearMessages,
    setMessages: setMessagesDirectly,
    cancelRequest,
    addMessage,
    updateMessage
  };
};
