/**
 * Logger específico para debug de problemas de autenticação no Dr. Will
 * Centraliza todos os logs relacionados ao fluxo de autenticação
 * 🔧 OTIMIZADO: Sistema de logs condicionais para performance
 */

// 🎯 CONFIGURAÇÃO DE PERFORMANCE
const DEBUG_ENABLED = process.env.NODE_ENV === 'development' && false; // ✅ Desabilitado por padrão
const VERBOSE_LOGS = false; // ✅ Logs verbosos apenas quando necessário
const MAX_EVENTS = DEBUG_ENABLED ? 100 : 0; // ✅ Sem armazenamento se desabilitado

interface AuthDebugEvent {
  timestamp: string;
  component: string;
  event: string;
  data: any;
  stackTrace?: string;
}

class AuthDebugLogger {
  private events: AuthDebugEvent[] = [];
  private maxEvents = MAX_EVENTS; // ✅ Configurável baseado no ambiente

  private addEvent(component: string, event: string, data: any, includeStack = false) {
    // 🔧 OTIMIZAÇÃO: Early return se debug desabilitado
    if (!DEBUG_ENABLED) return;

    const timestamp = new Date().toISOString();
    const stackTrace = includeStack && VERBOSE_LOGS ? new Error().stack?.split('\n').slice(2, 6).join('\n') : undefined;

    const authEvent: AuthDebugEvent = {
      timestamp,
      component,
      event,
      data,
      stackTrace
    };

    // ✅ Só armazenar se necessário
    if (this.maxEvents > 0) {
      this.events.push(authEvent);

      // Manter apenas os últimos eventos
      if (this.events.length > this.maxEvents) {
        this.events = this.events.slice(-this.maxEvents);
      }
    }

    // ✅ Log condicional no console
    if (VERBOSE_LOGS) {
      const prefix = `🔍 [${component}]`;
      // Logging disabled for production
    }
  }

  // AuthContext events
  authContextInit(data: any) {
    this.addEvent('AuthContext', 'initAuth started', data);
  }

  authContextCacheCheck(data: any) {
    this.addEvent('AuthContext', 'cache check', data);
  }

  authContextCacheLoaded(data: any) {
    this.addEvent('AuthContext', 'profile loaded from cache', data);
  }

  authContextSessionFromHook(data: any) {
    this.addEvent('AuthContext', 'using session from hook', data);
  }

  authContextSessionFromStorage(data: any) {
    this.addEvent('AuthContext', 'found session in storage', data);
  }

  authContextNoSession() {
    this.addEvent('AuthContext', 'no session found anywhere', {});
  }

  authContextSavedToStorage(data: any) {
    this.addEvent('AuthContext', 'saved to localStorage', data);
  }

  authContextCompleted(data: any) {
    this.addEvent('AuthContext', 'initAuth completed', data);
  }

  authContextSessionChange(data: any) {
    this.addEvent('AuthContext', 'session change detected', data);
  }

  authContextError(error: any) {
    this.addEvent('AuthContext', 'error', { error: error.message || error }, true);
  }

  // useDrWillHistory events
  historyHookInit(data: any) {
    this.addEvent('useDrWillHistory', 'hook initialized', data);
  }

  historyUserChanged(data: any) {
    this.addEvent('useDrWillHistory', 'user state changed', data, true);
  }

  historyCreateThreadCalled(data: any) {
    this.addEvent('useDrWillHistory', 'createNewThread called', data, true);
  }

  historyCreateThreadAuthCheck(data: any) {
    this.addEvent('useDrWillHistory', 'auth state check in createNewThread', data);
  }

  historyCreateThreadNoUser(data: any) {
    this.addEvent('useDrWillHistory', 'createNewThread failed - no user', data, true);
  }

  historyCreateThreadSuccess(data: any) {
    this.addEvent('useDrWillHistory', 'createNewThread success', data);
  }

  // useDrWillChat events
  chatSendMessageCalled(data: any) {
    this.addEvent('useDrWillChat', 'sendMessage called', data, true);
  }

  chatAuthStateCheck(data: any) {
    this.addEvent('useDrWillChat', 'auth state at sendMessage', data);
  }

  chatCreateThreadCalled(data: any) {
    this.addEvent('useDrWillChat', 'calling createNewThread', data);
  }

  chatCreateThreadResult(data: any) {
    this.addEvent('useDrWillChat', 'createNewThread result', data);
  }

  // FloatingChatButton events
  floatingChatUserState(data: any) {
    this.addEvent('FloatingChatButton', 'user state in handleSendMessage', data);
  }

  floatingChatHooksState(data: any) {
    this.addEvent('FloatingChatButton', 'hooks state in handleSendMessage', data);
  }

  // DrWill page events
  drWillUserState(data: any) {
    this.addEvent('DrWill', 'user state in handleSendMessage', data);
  }

  drWillSendMessageCall(data: any) {
    this.addEvent('DrWill', 'calling sendMessage', data);
  }

  // Utility methods
  getEvents(): AuthDebugEvent[] {
    return [...this.events];
  }

  getEventsByComponent(component: string): AuthDebugEvent[] {
    return this.events.filter(event => event.component === component);
  }

  getRecentEvents(count = 20): AuthDebugEvent[] {
    return this.events.slice(-count);
  }

  printSummary() {
    // Summary logging disabled for production
  }

  clear() {
    this.events = [];
  }
}

// Singleton instance
export const authDebugLogger = new AuthDebugLogger();

// Expose to window for debugging
if (typeof window !== 'undefined') {
  (window as any).authDebugLogger = authDebugLogger;
}
