import React from 'react';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';
import { Suggestion } from '@/utils/suggestionsParser';

interface SuggestionsPanelProps {
  suggestions: Suggestion[];
  onSuggestionClick: (action: string) => void;
  isLoading?: boolean;
}

/**
 * Componente simples para exibir sugestões do Dr. Will
 * Design clean e responsivo
 */
export const SuggestionsPanel: React.FC<SuggestionsPanelProps> = ({
  suggestions,
  onSuggestionClick,
  isLoading = false
}) => {
  // Não renderizar se não houver sugestões
  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-700"
    >
      {/* Header */}
      <div className="flex items-center gap-2 mb-3">
        <Sparkles className="h-4 w-4 text-blue-600 dark:text-blue-400" />
        <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
          Continue explorando
        </span>
      </div>

      {/* Sugestões */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
        {suggestions.map((suggestion, index) => (
          <motion.button
            key={index}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2, delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => !isLoading && onSuggestionClick(suggestion.action)}
            disabled={isLoading}
            className={`
              group relative overflow-hidden
              bg-white dark:bg-slate-800
              border border-blue-200 dark:border-blue-700
              hover:border-blue-300 dark:hover:border-blue-500
              hover:bg-blue-50 dark:hover:bg-blue-900/30
              transition-all duration-200
              text-blue-700 dark:text-blue-300
              hover:text-blue-800 dark:hover:text-blue-200
              rounded-md px-3 py-2
              text-sm font-medium
              text-left
              disabled:opacity-50 disabled:cursor-not-allowed
              disabled:hover:scale-100
            `}
          >
            {/* Efeito de hover */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            
            {/* Texto da sugestão */}
            <span className="relative z-10 block truncate">
              {suggestion.text}
            </span>
            
            {/* Indicador de loading */}
            {isLoading && (
              <div className="absolute inset-0 bg-white/50 dark:bg-slate-800/50 flex items-center justify-center">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              </div>
            )}
          </motion.button>
        ))}
      </div>

      {/* Footer opcional */}
      <div className="mt-3 text-xs text-blue-600/70 dark:text-blue-400/70 text-center">
        Clique em uma sugestão para continuar a conversa
      </div>
    </motion.div>
  );
};

export default SuggestionsPanel;
