import { supabase } from "@/integrations/supabase/client";
import type { User } from "@supabase/supabase-js";

interface OAuthProfileData {
  id: string;
  full_name?: string;
  avatar_url?: string;
  is_student: boolean;
  is_professional: boolean;
  formation_area?: string;
  graduation_year?: string | null;
  updated_at: string;
}

/**
 * Extrai dados do perfil do usuário OAuth (Google)
 */
export const extractOAuthProfileData = (user: User): Partial<OAuthProfileData> => {
  const metadata = user.user_metadata || {};
  
  // Dados do Google OAuth
  const fullName = metadata.full_name || metadata.name || '';
  const avatarUrl = metadata.avatar_url || metadata.picture || '';
  
  return {
    id: user.id,
    full_name: fullName,
    avatar_url: avatarUrl,
    // Padrões para novos usuários OAuth
    is_student: false,
    is_professional: true, // Assumir profissional por padrão
    formation_area: 'Não informado', // Será preenchido posteriormente pelo usuário
    graduation_year: 'Não informado',
    updated_at: new Date().toISOString(),
  };
};

/**
 * Verifica se é um novo usuário (primeira vez fazendo login)
 */
export const isNewOAuthUser = (user: User): boolean => {
  // Se created_at e last_sign_in_at são iguais (ou muito próximos), é um novo usuário
  const createdAt = new Date(user.created_at);
  const lastSignIn = new Date(user.last_sign_in_at || user.created_at);
  
  // Diferença de menos de 5 segundos indica novo usuário
  const timeDiff = Math.abs(lastSignIn.getTime() - createdAt.getTime());
  return timeDiff < 5000;
};

/**
 * Cria ou atualiza o perfil do usuário OAuth
 */
export const createOrUpdateOAuthProfile = async (user: User): Promise<{
  success: boolean;
  profile?: any;
  error?: string;
  isNewUser: boolean;
}> => {
  try {
    const profileData = extractOAuthProfileData(user);

    // Primeiro, verificar se o perfil já existe
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      // Erro diferente de "não encontrado"
      throw fetchError;
    }

    if (!existingProfile) {
      // Perfil não existe - criar novo
      const { data, error } = await supabase
        .from('profiles')
        .insert(profileData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return {
        success: true,
        profile: data,
        isNewUser: true
      };
    } else {
      // Perfil existe - atualizar apenas campos específicos do OAuth
      return updateExistingOAuthProfile(user, profileData, existingProfile);
    }
  } catch (error: any) {
    console.error('Erro ao criar/atualizar perfil OAuth:', error);
    return {
      success: false,
      error: error.message || 'Erro desconhecido',
      isNewUser: false
    };
  }
};

/**
 * Atualiza perfil existente com dados do OAuth
 */
const updateExistingOAuthProfile = async (
  user: User,
  profileData: Partial<OAuthProfileData>,
  existingProfile: any
): Promise<{
  success: boolean;
  profile?: any;
  error?: string;
  isNewUser: boolean;
}> => {
  try {
    // Preparar dados para atualização (apenas campos que podem ser atualizados via OAuth)
    const updateData: Partial<OAuthProfileData> = {
      updated_at: new Date().toISOString(),
    };

    let hasChanges = false;

    // Atualizar avatar apenas se não existir ou se mudou
    if (profileData.avatar_url &&
        (!existingProfile?.avatar_url || existingProfile.avatar_url !== profileData.avatar_url)) {
      updateData.avatar_url = profileData.avatar_url;
      hasChanges = true;
    }

    // Atualizar nome apenas se não existir ou se mudou
    if (profileData.full_name &&
        (!existingProfile?.full_name || existingProfile.full_name !== profileData.full_name)) {
      updateData.full_name = profileData.full_name;
      hasChanges = true;
    }

    // Se não há mudanças, retornar o perfil existente
    if (!hasChanges) {
      return {
        success: true,
        profile: existingProfile,
        isNewUser: false
      };
    }

    const { data, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      profile: data,
      isNewUser: false
    };
  } catch (error: any) {
    console.error('Erro ao atualizar perfil OAuth:', error);
    return {
      success: false,
      error: error.message || 'Erro ao atualizar perfil',
      isNewUser: false
    };
  }
};

/**
 * Verifica se o usuário precisa completar seu perfil
 */
export const needsProfileCompletion = (profile: any): boolean => {
  if (!profile) return true;
  
  // Verificar campos obrigatórios para profissionais de saúde
  const requiredFields = [
    'formation_area', // Área de formação
    'graduation_year', // Ano de formação
  ];

  return requiredFields.some(field => !profile[field]);
};

/**
 * Gera mensagem de boas-vindas personalizada
 */
export const generateWelcomeMessage = (user: User, isNewUser: boolean): {
  title: string;
  description: string;
} => {
  const firstName = user.user_metadata?.full_name?.split(' ')[0] || 
                   user.user_metadata?.name?.split(' ')[0] || '';
  
  const hora = new Date().getHours();
  let saudacao = "Bem-vindo";

  if (hora >= 5 && hora < 12) {
    saudacao = "Bom dia";
  } else if (hora >= 12 && hora < 18) {
    saudacao = "Boa tarde";
  } else {
    saudacao = "Boa noite";
  }

  if (isNewUser) {
    return {
      title: `${saudacao}${firstName ? `, ${firstName}` : ''}!`,
      description: "Sua conta foi criada com sucesso. Complete seu perfil quando desejar para uma experiência personalizada."
    };
  } else {
    return {
      title: `${saudacao}${firstName ? `, ${firstName}` : ''} de volta!`,
      description: "Login realizado com sucesso."
    };
  }
};
