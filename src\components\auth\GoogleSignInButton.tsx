import { useState } from "react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useNotification } from "@/context/NotificationContext";
import { Loader2 } from "lucide-react";

interface GoogleSignInButtonProps {
  onSuccess?: () => void;
  mode?: "signin" | "signup";
  disabled?: boolean;
}

const GoogleIcon = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
      fill="#4285F4"
    />
    <path
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
      fill="#34A853"
    />
    <path
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
      fill="#FBBC05"
    />
    <path
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
      fill="#EA4335"
    />
  </svg>
);

export const GoogleSignInButton = ({
  onSuccess,
  mode = "signin",
  disabled = false
}: GoogleSignInButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showNotification } = useNotification();

  // Detectar se está em WebView
  const isWebView = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isAndroid = /android/i.test(ua);
    const isWebView = /wv/.test(ua) || /webview/.test(ua);
    const hasAndroidInterface = 'Android' in window || 'AndroidInterface' in window;
    const isChrome = /chrome/.test(ua) && !/edg/.test(ua);
    const isSamsung = /samsungbrowser/.test(ua);

    return isAndroid && (isWebView || hasAndroidInterface || (!isChrome && !isSamsung));
  };

  // Se está em WebView, não renderizar o botão
  if (isWebView()) {
    return null;
  }

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);

      // Detectar ambiente e configurar URL de redirecionamento adequada
      const isLocalhost = window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1';

      const redirectUrl = isLocalhost
        ? `http://localhost:8080/auth/callback`
        : 'https://pedb.com.br/auth/callback';

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        throw error;
      }

      // O redirecionamento será automático, mas podemos mostrar uma mensagem
      showNotification({
        title: "Redirecionando...",
        description: "Você será redirecionado para o Google para fazer login.",
        type: "info",
        buttonText: "Ok"
      });

    } catch (error: any) {
      let errorMessage = "Erro inesperado ao fazer login com Google.";
      
      if (error.message?.includes('popup')) {
        errorMessage = "Pop-up bloqueado. Por favor, permita pop-ups para este site.";
      } else if (error.message?.includes('network')) {
        errorMessage = "Erro de conexão. Verifique sua internet e tente novamente.";
      } else if (error.message?.includes('unauthorized')) {
        errorMessage = "Acesso não autorizado. Verifique as configurações do Google OAuth.";
      }

      showNotification({
        title: "Erro no login",
        description: errorMessage,
        type: "error",
        buttonText: "Tentar novamente"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const buttonText = mode === "signin" 
    ? "Entrar com Google" 
    : "Criar conta com Google";

  return (
    <div className="relative">
      <Button
        type="button"
        variant="outline"
        onClick={handleGoogleSignIn}
        disabled={disabled || isLoading}
        className="w-full flex items-center justify-center gap-3 h-11 border-gray-300 hover:bg-gray-50 transition-colors"
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <GoogleIcon />
        )}
        <span className="text-sm font-medium text-gray-700">
          {isLoading ? "Conectando..." : buttonText}
        </span>
      </Button>

      {/* Badge "Novo" */}
      <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-md">
        Novo
      </div>
    </div>
  );
};

export default GoogleSignInButton;
