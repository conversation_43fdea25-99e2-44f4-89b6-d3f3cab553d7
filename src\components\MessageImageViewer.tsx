import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, X, ZoomIn } from 'lucide-react';

interface MessageImageViewerProps {
  images: string[];
  className?: string;
}

export const MessageImageViewer: React.FC<MessageImageViewerProps> = ({ images, className = '' }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogImageIndex, setDialogImageIndex] = useState(0);

  if (!images || images.length === 0) return null;

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const openDialog = (index: number) => {
    setDialogImageIndex(index);
    setIsDialogOpen(true);
  };

  const nextDialogImage = () => {
    setDialogImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevDialogImage = () => {
    setDialogImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  return (
    <>
      {/* Visualização compacta */}
      <div className={`relative mb-2 ${className}`}>
        <div className="relative group">
          <img
            src={images[currentIndex]}
            alt={`Imagem ${currentIndex + 1}`}
            className="max-w-full max-h-48 rounded-lg object-cover cursor-pointer hover:opacity-90 transition-opacity"
            onClick={() => openDialog(currentIndex)}
          />
          
          {/* Botão de zoom */}
          <Button
            variant="secondary"
            size="sm"
            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 hover:bg-black/70 text-white border-none"
            onClick={() => openDialog(currentIndex)}
          >
            <ZoomIn className="w-4 h-4" />
          </Button>

          {/* Controles do carrossel (apenas se houver múltiplas imagens) */}
          {images.length > 1 && (
            <>
              <Button
                variant="secondary"
                size="sm"
                className="absolute left-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 hover:bg-black/70 text-white border-none"
                onClick={prevImage}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 hover:bg-black/70 text-white border-none"
                onClick={nextImage}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>

              {/* Indicadores */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                {images.map((_, index) => (
                  <button
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                    onClick={() => setCurrentIndex(index)}
                  />
                ))}
              </div>
            </>
          )}
        </div>

        {/* Contador de imagens */}
        {images.length > 1 && (
          <div className="text-xs text-gray-500 mt-1 text-center">
            {currentIndex + 1} de {images.length}
          </div>
        )}
      </div>

      {/* Dialog para visualização expandida */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        {/* Overlay customizado com z-index alto */}
        {isDialogOpen && (
          <div
            className="fixed inset-0 z-[79] bg-black/80"
            onClick={() => setIsDialogOpen(false)}
          />
        )}
        <DialogContent className="max-w-4xl max-h-[90vh] p-0 z-[80]">
          <DialogHeader className="p-4 pb-0">
            <DialogTitle className="flex items-center justify-between">
              <span>Imagem {dialogImageIndex + 1} de {images.length}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsDialogOpen(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          <div className="relative p-4">
            <img
              src={images[dialogImageIndex]}
              alt={`Imagem ${dialogImageIndex + 1}`}
              className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
            />

            {/* Controles do dialog (apenas se houver múltiplas imagens) */}
            {images.length > 1 && (
              <>
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white border-none"
                  onClick={prevDialogImage}
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white border-none"
                  onClick={nextDialogImage}
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>

                {/* Indicadores do dialog */}
                <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {images.map((_, index) => (
                    <button
                      key={index}
                      className={`w-3 h-3 rounded-full transition-colors ${
                        index === dialogImageIndex ? 'bg-white' : 'bg-white/50'
                      }`}
                      onClick={() => setDialogImageIndex(index)}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
