import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useSupabaseClient, useSession } from '@supabase/auth-helpers-react';
import type { User } from '@supabase/supabase-js';
import { authDebugLogger } from '@/utils/authDebugLogger';

// Interface para o perfil do usuário
interface UserProfile {
  id: string;
  email?: string; // Adicionar email
  full_name?: string;
  avatar_url?: string;
  is_admin?: boolean;
  [key: string]: any;
}

// Interface para o contexto de autenticação
interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  isAdmin: boolean;
  isLoading: boolean;
  signOut: () => Promise<void>;
  invalidateProfileCache: (userId: string) => void;
  refreshProfile: (userId: string, userEmail?: string) => Promise<UserProfile | null>;
}

// Criar o contexto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook para usar o contexto
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const session = useSession();
  const supabase = useSupabaseClient();
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [profileCache, setProfileCache] = useState<Record<string, UserProfile>>({});
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Função para invalidar cache do perfil (útil após OAuth)
  const invalidateProfileCache = useCallback((userId: string) => {
    setProfileCache(prev => {
      const newCache = { ...prev };
      delete newCache[userId];
      return newCache;
    });
    localStorage.removeItem('auth_profile');
    localStorage.removeItem('auth_user_id');
  }, []);

  // 🔧 OTIMIZAÇÃO: Memoizar fetchUserProfile para evitar recriação constante
  const fetchUserProfile = useCallback(async (userId: string, userEmail?: string, forceRefresh = false) => {
    try {
      // Verificar se já temos o perfil em cache (apenas se não forçar refresh)
      if (!forceRefresh && profileCache[userId]) {
        const cachedProfile = { ...profileCache[userId], email: userEmail };
        setProfile(cachedProfile);
        setIsAdmin(cachedProfile?.is_admin || false);
        return cachedProfile;
      }

      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        return null;
      }

      // Adicionar email ao perfil
      const profileWithEmail = { ...data, email: userEmail };

      // Atualizar o cache
      setProfileCache(prev => ({
        ...prev,
        [userId]: profileWithEmail
      }));

      setProfile(profileWithEmail);
      setIsAdmin(profileWithEmail?.is_admin || false);
      return profileWithEmail;
    } catch (error) {
      return null;
    }
  }, [supabase]); // 🔧 OTIMIZAÇÃO: Removido profileCache da dependência

  // Função para forçar refresh do perfil
  const refreshProfile = useCallback(async (userId: string, userEmail?: string) => {
    return await fetchUserProfile(userId, userEmail, true);
  }, [fetchUserProfile]);

  // Inicializar autenticação
  useEffect(() => {
    // Evitar inicialização múltipla
    if (isInitialized) return;

    const initAuth = async () => {
      // Iniciar processo de autenticação

      // 🔍 DEBUG: Log do estado inicial da autenticação
      authDebugLogger.authContextInit({
        hasSession: !!session,
        sessionUserId: session?.user?.id,
        sessionEmail: session?.user?.email,
        isInitialized
      });

      try {
        // Tentar carregar o perfil do cache local se disponível
        const cachedProfileData = localStorage.getItem('auth_profile');
        const cachedUserId = localStorage.getItem('auth_user_id');

        authDebugLogger.authContextCacheCheck({
          hasCachedProfile: !!cachedProfileData,
          cachedUserId
        });

        if (cachedProfileData && cachedUserId) {
          try {
            const parsedProfile = JSON.parse(cachedProfileData);
            // Usando perfil em cache local temporariamente
            setProfile(parsedProfile);
            setIsAdmin(parsedProfile?.is_admin || false);

            authDebugLogger.authContextCacheLoaded({
              userId: cachedUserId,
              profileId: parsedProfile?.id,
              isAdmin: parsedProfile?.is_admin
            });

            // Pré-popular o cache de perfil
            setProfileCache(prev => ({
              ...prev,
              [cachedUserId]: parsedProfile
            }));
          } catch (e) {
            // Erro ao parsear perfil em cache, remover dados inválidos
            authDebugLogger.authContextError(e);
            localStorage.removeItem('auth_profile');
            localStorage.removeItem('auth_user_id');
          }
        }

        // Se já temos uma sessão do hook, usamos ela
        if (session !== undefined) {
          authDebugLogger.authContextSessionFromHook({
            hasUser: !!session?.user,
            userId: session?.user?.id,
            email: session?.user?.email
          });

          setUser(session?.user ?? null);

          if (session?.user) {
            const profile = await fetchUserProfile(session.user.id, session.user.email);

            // Salvar no localStorage para uso futuro
            if (profile) {
              localStorage.setItem('auth_profile', JSON.stringify(profile));
              localStorage.setItem('auth_user_id', session.user.id);
              authDebugLogger.authContextSavedToStorage({ userId: session.user.id, source: 'session' });
            }
          } else {
            authDebugLogger.authContextNoSession();
            setProfile(null);
            setIsAdmin(false);
            localStorage.removeItem('auth_profile');
            localStorage.removeItem('auth_user_id');
          }
        } else {
          // Se não temos sessão do hook, tentamos buscar do storage
          const { data } = await supabase.auth.getSession();

          if (data.session) {
            // Sessão encontrada no storage
            authDebugLogger.authContextSessionFromStorage({
              userId: data.session.user.id,
              email: data.session.user.email
            });

            setUser(data.session.user);
            const profile = await fetchUserProfile(data.session.user.id, data.session.user.email);

            // Salvar no localStorage para uso futuro
            if (profile) {
              localStorage.setItem('auth_profile', JSON.stringify(profile));
              localStorage.setItem('auth_user_id', data.session.user.id);
              authDebugLogger.authContextSavedToStorage({ userId: data.session.user.id, source: 'storage' });
            }
          } else {
            // Nenhuma sessão encontrada
            authDebugLogger.authContextNoSession();
            setUser(null);
            setProfile(null);
            setIsAdmin(false);
            localStorage.removeItem('auth_profile');
            localStorage.removeItem('auth_user_id');
          }
        }
      } catch (error) {
        // Erro ao inicializar autenticação
        authDebugLogger.authContextError(error);
      } finally {
        setIsLoading(false);
        setIsInitialized(true);

        authDebugLogger.authContextCompleted({
          hasUser: !!user,
          userId: user?.id,
          isLoading: false,
          isInitialized: true
        });
      }
    };

    initAuth();
  }, [session, supabase, isInitialized]); // 🔧 OTIMIZAÇÃO: Removido fetchUserProfile da dependência

  // Monitorar mudanças na sessão
  useEffect(() => {
    if (!isInitialized) return;

    const handleSessionChange = async () => {
      // 🔍 DEBUG: Log da mudança de sessão
      authDebugLogger.authContextSessionChange({
        hasSession: !!session,
        sessionUserId: session?.user?.id,
        sessionEmail: session?.user?.email,
        isInitialized
      });

      // Atualizar estado com base na nova sessão
      setUser(session?.user ?? null);

      if (session?.user) {
        const profile = await fetchUserProfile(session.user.id, session.user.email);

        // Salvar no localStorage para uso futuro
        if (profile) {
          localStorage.setItem('auth_profile', JSON.stringify(profile));
          localStorage.setItem('auth_user_id', session.user.id);
          authDebugLogger.authContextSavedToStorage({ userId: session.user.id, source: 'sessionChange' });
        }
      } else {
        setProfile(null);
        setIsAdmin(false);
        localStorage.removeItem('auth_profile');
        localStorage.removeItem('auth_user_id');
      }
    };

    handleSessionChange();
  }, [session, isInitialized]); // 🔧 OTIMIZAÇÃO: Removido fetchUserProfile da dependência

  // Função para fazer logout
  const signOut = async () => {
    setIsLoading(true);
    await supabase.auth.signOut();
    setUser(null);
    setProfile(null);
    setIsAdmin(false);
    localStorage.removeItem('auth_profile');
    localStorage.removeItem('auth_user_id');
    setIsLoading(false);
  };

  const value = {
    user,
    profile,
    isAdmin,
    isLoading,
    signOut,
    invalidateProfileCache,
    refreshProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
